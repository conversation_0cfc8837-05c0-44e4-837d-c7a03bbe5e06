/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/sign-in`; params?: Router.UnknownInputParams; } | { pathname: `/root/index`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `/root/tabs/explore`; params?: Router.UnknownInputParams; } | { pathname: `/root/tabs`; params?: Router.UnknownInputParams; } | { pathname: `/root/tabs/profile`; params?: Router.UnknownInputParams; } | { pathname: `/root/tabs/properties/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/sign-in`; params?: Router.UnknownOutputParams; } | { pathname: `/root/index`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `/root/tabs/explore`; params?: Router.UnknownOutputParams; } | { pathname: `/root/tabs`; params?: Router.UnknownOutputParams; } | { pathname: `/root/tabs/profile`; params?: Router.UnknownOutputParams; } | { pathname: `/root/tabs/properties/[id]`, params: Router.UnknownOutputParams & { id: string; } };
      href: Router.RelativePathString | Router.ExternalPathString | `/sign-in${`?${string}` | `#${string}` | ''}` | `/root/index${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `/root/tabs/explore${`?${string}` | `#${string}` | ''}` | `/root/tabs${`?${string}` | `#${string}` | ''}` | `/root/tabs/profile${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/sign-in`; params?: Router.UnknownInputParams; } | { pathname: `/root/index`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `/root/tabs/explore`; params?: Router.UnknownInputParams; } | { pathname: `/root/tabs`; params?: Router.UnknownInputParams; } | { pathname: `/root/tabs/profile`; params?: Router.UnknownInputParams; } | `/root/tabs/properties/${Router.SingleRoutePart<T>}${`?${string}` | `#${string}` | ''}` | { pathname: `/root/tabs/properties/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
    }
  }
}
