{"name": "prettier-plugin-tailwindcss", "version": "0.5.11", "description": "A Prettier plugin for sorting Tailwind CSS classes.", "license": "MIT", "main": "dist/index.mjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist"], "repository": {"type": "git", "url": "https://github.com/tailwindlabs/prettier-plugin-tailwindcss"}, "bugs": {"url": "https://github.com/tailwindlabs/prettier-plugin-tailwindcss/issues"}, "scripts": {"_pre": "rimraf dist && cpy 'node_modules/tailwindcss/lib/css/*' dist/css", "_esbuild": "node build.mjs", "prebuild": "npm run _pre", "build": "npm run _esbuild -- --minify", "predev": "npm run _pre", "dev": "npm run _esbuild -- --watch", "pretest": "node scripts/install-fixture-deps.js", "test": "NODE_OPTIONS=--experimental-vm-modules jest", "prepublishOnly": "npm run build && node scripts/copy-licenses.js", "format": "prettier \"src/**/*.js\" \"scripts/**/*.js\" \"tests/test.js\" --write --print-width 100 --single-quote --no-semi --plugin-search-dir ./tests", "release-channel": "node ./scripts/release-channel.js", "release-notes": "node ./scripts/release-notes.js"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "^4.1.0", "@prettier/plugin-pug": "^3.0", "@shopify/prettier-plugin-liquid": "^1.4.0", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "clear-module": "^4.1.2", "cpy-cli": "^5.0.0", "esbuild": "^0.19.8", "escalade": "^3.1.1", "import-sort-style-module": "^6.0.0", "jest": "^29.6.2", "jsesc": "^2.5.2", "license-checker": "^25.0.1", "line-column": "^1.0.2", "marko": "^5.31.18", "prettier": "^3.1", "prettier-plugin-astro": "^0.12.2", "prettier-plugin-css-order": "^2.0.0", "prettier-plugin-import-sort": "^0.0.7", "prettier-plugin-jsdoc": "^1.0.1", "prettier-plugin-marko": "^3.1.1", "prettier-plugin-organize-attributes": "^1.0.0", "prettier-plugin-organize-imports": "^3.2.4", "prettier-plugin-style-order": "^0.2.2", "prettier-plugin-svelte": "^3.1.2", "recast": "0.20.5", "resolve-from": "^5.0.0", "rimraf": "^5.0.5", "svelte": "^4.2.8", "tailwindcss": "^3.4.1"}, "peerDependencies": {"@ianvs/prettier-plugin-sort-imports": "*", "@prettier/plugin-pug": "*", "@shopify/prettier-plugin-liquid": "*", "@trivago/prettier-plugin-sort-imports": "*", "prettier": "^3.0", "prettier-plugin-astro": "*", "prettier-plugin-css-order": "*", "prettier-plugin-import-sort": "*", "prettier-plugin-jsdoc": "*", "prettier-plugin-marko": "*", "prettier-plugin-organize-attributes": "*", "prettier-plugin-organize-imports": "*", "prettier-plugin-style-order": "*", "prettier-plugin-svelte": "*"}, "peerDependenciesMeta": {"@ianvs/prettier-plugin-sort-imports": {"optional": true}, "@prettier/plugin-pug": {"optional": true}, "@shopify/prettier-plugin-liquid": {"optional": true}, "@trivago/prettier-plugin-sort-imports": {"optional": true}, "prettier-plugin-astro": {"optional": true}, "prettier-plugin-css-order": {"optional": true}, "prettier-plugin-import-sort": {"optional": true}, "prettier-plugin-jsdoc": {"optional": true}, "prettier-plugin-marko": {"optional": true}, "prettier-plugin-organize-attributes": {"optional": true}, "prettier-plugin-organize-imports": {"optional": true}, "prettier-plugin-style-order": {"optional": true}, "prettier-plugin-svelte": {"optional": true}, "prettier-plugin-twig-melody": {"optional": true}}, "engines": {"node": ">=14.21.3"}, "importSort": {".js, .jsx, .ts, .tsx": {"style": "module"}}, "jest": {"testTimeout": 15000}}